//
//  CAGlassmorphism.swift
//  TestCAwigdet
//
//  Created by Core Animation UI Framework
//

import Cocoa
import QuartzCore

// MARK: - 玻璃拟态样式配置
struct GlassmorphismConfig {
    // 背景
    var backgroundColor: NSColor = NSColor.white.withAlphaComponent(0.1)
    var backgroundBlurRadius: CGFloat = 20.0
    
    // 边框
    var borderColor: NSColor = NSColor.white.withAlphaComponent(0.2)
    var borderWidth: CGFloat = 1.0
    
    // 阴影
    var shadowColor: NSColor = NSColor.black.withAlphaComponent(0.1)
    var shadowOffset: CGSize = CGSize(width: 0, height: 4)
    var shadowRadius: CGFloat = 12.0
    
    // 形状
    var cornerRadius: CGFloat = 12.0
    
    // 高光
    var highlightColor: NSColor = NSColor.white.withAlphaComponent(0.4)
    var highlightPosition: CGFloat = 0.1 // 0.0 到 1.0，表示高光位置
    
    // 动画
    var animationDuration: TimeInterval = 0.3
    var hoverScale: CGFloat = 1.02
    var pressScale: CGFloat = 0.98
}

// MARK: - 玻璃拟态样式应用器
class CAGlassmorphism {
    
    static let shared = CAGlassmorphism()
    
    private init() {}
    
    // MARK: - 应用玻璃拟态效果
    func applyGlassmorphism(to layer: CALayer, config: GlassmorphismConfig = GlassmorphismConfig()) {
        // 设置基础属性
        layer.cornerRadius = config.cornerRadius
        layer.masksToBounds = false
        
        // 创建背景层
        let backgroundLayer = createBackgroundLayer(config: config)
        layer.insertSublayer(backgroundLayer, at: 0)
        
        // 创建边框层
        let borderLayer = createBorderLayer(config: config)
        layer.addSublayer(borderLayer)
        
        // 创建高光层
        let highlightLayer = createHighlightLayer(config: config)
        layer.addSublayer(highlightLayer)
        
        // 创建阴影
        applyShadow(to: layer, config: config)
        
        // 应用背景模糊（如果支持）
        applyBackgroundBlur(to: layer, config: config)
    }
    
    // MARK: - 创建背景层
    private func createBackgroundLayer(config: GlassmorphismConfig) -> CAShapeLayer {
        let backgroundLayer = CAShapeLayer()
        backgroundLayer.fillColor = config.backgroundColor.cgColor
        backgroundLayer.strokeColor = NSColor.clear.cgColor
        backgroundLayer.cornerRadius = config.cornerRadius
        return backgroundLayer
    }
    
    // MARK: - 创建边框层
    private func createBorderLayer(config: GlassmorphismConfig) -> CAShapeLayer {
        let borderLayer = CAShapeLayer()
        borderLayer.fillColor = NSColor.clear.cgColor
        borderLayer.strokeColor = config.borderColor.cgColor
        borderLayer.lineWidth = config.borderWidth
        borderLayer.cornerRadius = config.cornerRadius
        return borderLayer
    }
    
    // MARK: - 创建高光层
    private func createHighlightLayer(config: GlassmorphismConfig) -> CAGradientLayer {
        let highlightLayer = CAGradientLayer()
        
        let highlightColor = config.highlightColor.cgColor
        let transparentColor = config.highlightColor.withAlphaComponent(0.0).cgColor
        
        highlightLayer.colors = [highlightColor, transparentColor]
        highlightLayer.locations = [0.0, 1.0]
        highlightLayer.startPoint = CGPoint(x: 0.0, y: 0.0)
        highlightLayer.endPoint = CGPoint(x: 1.0, y: config.highlightPosition)
        highlightLayer.cornerRadius = config.cornerRadius
        
        return highlightLayer
    }
    
    // MARK: - 应用阴影
    private func applyShadow(to layer: CALayer, config: GlassmorphismConfig) {
        layer.shadowColor = config.shadowColor.cgColor
        layer.shadowOffset = config.shadowOffset
        layer.shadowRadius = config.shadowRadius
        layer.shadowOpacity = 1.0
        
        // 创建阴影路径以提高性能
        let shadowPath = NSBezierPath(roundedRect: layer.bounds, 
                                     xRadius: config.cornerRadius, 
                                     yRadius: config.cornerRadius)
        layer.shadowPath = shadowPath.cgPath
    }
    
    // MARK: - 应用背景模糊
    private func applyBackgroundBlur(to layer: CALayer, config: GlassmorphismConfig) {
        // 在 macOS 上使用 NSVisualEffectView 的效果
        if #available(macOS 10.10, *) {
            let blurFilter = CIFilter(name: "CIGaussianBlur")
            blurFilter?.setValue(config.backgroundBlurRadius, forKey: kCIInputRadiusKey)
            layer.backgroundFilters = [blurFilter].compactMap { $0 }
        }
    }
    
    // MARK: - 更新层框架
    func updateLayerFrames(in parentLayer: CALayer) {
        let bounds = parentLayer.bounds
        
        for sublayer in parentLayer.sublayers ?? [] {
            if sublayer is CAShapeLayer || sublayer is CAGradientLayer {
                sublayer.frame = bounds
                
                if let shapeLayer = sublayer as? CAShapeLayer {
                    let path = NSBezierPath(roundedRect: bounds, 
                                          xRadius: sublayer.cornerRadius, 
                                          yRadius: sublayer.cornerRadius)
                    shapeLayer.path = path.cgPath
                }
            }
        }
        
        // 更新阴影路径
        let shadowPath = NSBezierPath(roundedRect: bounds, 
                                     xRadius: parentLayer.cornerRadius, 
                                     yRadius: parentLayer.cornerRadius)
        parentLayer.shadowPath = shadowPath.cgPath
    }
    
    // MARK: - 交互动画
    func animateHover(layer: CALayer, isHovering: Bool, config: GlassmorphismConfig = GlassmorphismConfig()) {
        let scale = isHovering ? config.hoverScale : 1.0
        let opacity = isHovering ? 0.8 : 1.0
        
        CATransaction.begin()
        CATransaction.setAnimationDuration(config.animationDuration)
        CATransaction.setAnimationTimingFunction(CAMediaTimingFunction(name: .easeInEaseOut))
        
        layer.transform = CATransform3DMakeScale(scale, scale, 1.0)
        layer.opacity = Float(opacity)
        
        CATransaction.commit()
    }
    
    func animatePress(layer: CALayer, isPressed: Bool, config: GlassmorphismConfig = GlassmorphismConfig()) {
        let scale = isPressed ? config.pressScale : 1.0
        
        CATransaction.begin()
        CATransaction.setAnimationDuration(config.animationDuration * 0.5)
        CATransaction.setAnimationTimingFunction(CAMediaTimingFunction(name: .easeOut))
        
        layer.transform = CATransform3DMakeScale(scale, scale, 1.0)
        
        CATransaction.commit()
    }
    
    // MARK: - 预设样式
    static func lightGlass() -> GlassmorphismConfig {
        var config = GlassmorphismConfig()
        config.backgroundColor = NSColor.white.withAlphaComponent(0.15)
        config.borderColor = NSColor.white.withAlphaComponent(0.3)
        config.highlightColor = NSColor.white.withAlphaComponent(0.5)
        return config
    }
    
    static func darkGlass() -> GlassmorphismConfig {
        var config = GlassmorphismConfig()
        config.backgroundColor = NSColor.black.withAlphaComponent(0.15)
        config.borderColor = NSColor.white.withAlphaComponent(0.1)
        config.highlightColor = NSColor.white.withAlphaComponent(0.2)
        config.shadowColor = NSColor.black.withAlphaComponent(0.3)
        return config
    }
    
    static func coloredGlass(color: NSColor) -> GlassmorphismConfig {
        var config = GlassmorphismConfig()
        config.backgroundColor = color.withAlphaComponent(0.2)
        config.borderColor = color.withAlphaComponent(0.4)
        config.highlightColor = NSColor.white.withAlphaComponent(0.3)
        return config
    }
    
    static func accentGlass() -> GlassmorphismConfig {
        var config = GlassmorphismConfig()
        config.backgroundColor = NSColor.controlAccentColor.withAlphaComponent(0.2)
        config.borderColor = NSColor.controlAccentColor.withAlphaComponent(0.4)
        config.highlightColor = NSColor.white.withAlphaComponent(0.4)
        return config
    }
}

// MARK: - NSBezierPath 扩展
extension NSBezierPath {
    var cgPath: CGPath {
        let path = CGMutablePath()
        var points = [CGPoint](repeating: .zero, count: 3)
        
        for i in 0..<elementCount {
            let type = element(at: i, associatedPoints: &points)
            switch type {
            case .moveTo:
                path.move(to: points[0])
            case .lineTo:
                path.addLine(to: points[0])
            case .curveTo:
                path.addCurve(to: points[2], control1: points[0], control2: points[1])
            case .cubicCurveTo:
                path.addCurve(to: points[2], control1: points[0], control2: points[1])
            case .quadraticCurveTo:
                path.addQuadCurve(to: points[1], control: points[0])
            case .closePath:
                path.closeSubpath()
            @unknown default:
                break
            }
        }
        
        return path
    }
}
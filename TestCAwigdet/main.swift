//
//  main.swift
//  TestCAwigdet
//
//  Created by 王雷 on 10/6/25.
//

import Cocoa

// 创建应用程序实例
let app = NSApplication.shared

// 设置应用程序代理
class AppDelegate: NSObject, NSApplicationDelegate {
    var window: NSWindow!

    func applicationDidFinishLaunching(_ notification: Notification) {
        // 获取屏幕尺寸
        guard let screen = NSScreen.main else { return }
        let screenRect = screen.visibleFrame

        print("Screen size: \(screenRect)")

        // 创建一个大窗口，使用屏幕的完整可见区域
        window = NSWindow(
            contentRect: screenRect,
            styleMask: [.titled, .closable, .miniaturizable, .resizable],
            backing: .buffered,
            defer: false
        )

        window.title = "TestCAwigdet - Core Animation UI Components"

        // 创建主视图控制器
        let viewController = ViewController()
        window.contentViewController = viewController

        // 显示窗口
        window.makeKeyAndOrderFront(nil)

        // 在显示后再次设置frame确保全屏
        DispatchQueue.main.async {
            self.window.setFrame(screenRect, display: true, animate: false)
            print("Final window frame: \(self.window.frame)")
        }

        // 激活应用程序
        NSApp.activate(ignoringOtherApps: true)
    }

    func applicationShouldTerminateWhenLastWindowClosed(_ sender: NSApplication) -> Bool {
        return true
    }
}

// 设置应用程序代理
let delegate = AppDelegate()
app.delegate = delegate

// 运行应用程序
app.run()

//
//  CAUIBase.swift
//  TestCAwigdet
//
//  Created by Core Animation UI Framework
//

import Cocoa
import QuartzCore

// MARK: - 基础UI组件协议
protocol CAUIComponent {
    var isEnabled: Bool { get set }
    var isHighlighted: Bool { get set }
    var isSelected: Bool { get set }
    
    func setupLayers()
    func updateAppearance()
    func animateToState(_ state: CAUIState)
}

// MARK: - UI状态枚举
enum CAUIState {
    case normal
    case highlighted
    case selected
    case disabled
    case focused
}

// MARK: - 基础UI组件类
class CAUIBase: NSView, CAUIComponent {
    
    // MARK: - Properties
    var isEnabled: Bool = true {
        didSet {
            updateAppearance()
        }
    }
    
    var isHighlighted: Bool = false {
        didSet {
            if oldValue != isHighlighted {
                animateToState(isHighlighted ? .highlighted : .normal)
            }
        }
    }
    
    var isSelected: Bool = false {
        didSet {
            if oldValue != isSelected {
                animateToState(isSelected ? .selected : .normal)
            }
        }
    }
    
    // MARK: - Layer Properties
    var backgroundLayer: CAShapeLayer!
    var borderLayer: CAShapeLayer!
    var shadowLayer: CAShapeLayer!
    
    // MARK: - Animation Properties
    var animationDuration: TimeInterval = 0.25
    var animationTimingFunction: CAMediaTimingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
    
    // MARK: - Initialization
    override init(frame frameRect: NSRect) {
        super.init(frame: frameRect)
        commonInit()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        commonInit()
    }
    
    private func commonInit() {
        wantsLayer = true
        setupLayers()
        updateAppearance()
    }
    
    // MARK: - Layer Setup
    func setupLayers() {
        // 背景层
        backgroundLayer = CAShapeLayer()
        backgroundLayer.fillColor = NSColor.controlBackgroundColor.cgColor
        layer?.addSublayer(backgroundLayer)
        
        // 边框层
        borderLayer = CAShapeLayer()
        borderLayer.fillColor = NSColor.clear.cgColor
        borderLayer.strokeColor = NSColor.controlColor.cgColor
        borderLayer.lineWidth = 1.0
        layer?.addSublayer(borderLayer)
        
        // 阴影层
        shadowLayer = CAShapeLayer()
        shadowLayer.fillColor = NSColor.black.withAlphaComponent(0.1).cgColor
        layer?.insertSublayer(shadowLayer, at: 0)
    }
    
    // MARK: - Layout
    override func layout() {
        super.layout()
        updateLayerFrames()
    }
    
    func updateLayerFrames() {
        let bounds = self.bounds
        let cornerRadius: CGFloat = 8.0
        let path = NSBezierPath(roundedRect: bounds, xRadius: cornerRadius, yRadius: cornerRadius)
        let cgPath = path.cgPath
        
        backgroundLayer.path = cgPath
        borderLayer.path = cgPath
        
        // 阴影偏移
        let shadowBounds = bounds.offsetBy(dx: 0, dy: -2)
        let shadowPath = NSBezierPath(roundedRect: shadowBounds, xRadius: cornerRadius, yRadius: cornerRadius)
        shadowLayer.path = shadowPath.cgPath
    }
    
    // MARK: - Appearance Updates
    func updateAppearance() {
        guard let layer = self.layer else { return }
        
        let currentState = getCurrentState()
        
        switch currentState {
        case .normal:
            backgroundLayer.fillColor = NSColor.controlBackgroundColor.cgColor
            borderLayer.strokeColor = NSColor.controlColor.cgColor
            layer.opacity = 1.0
            
        case .highlighted:
            backgroundLayer.fillColor = NSColor.selectedControlColor.cgColor
            borderLayer.strokeColor = NSColor.controlAccentColor.cgColor
            layer.opacity = 0.8
            
        case .selected:
            backgroundLayer.fillColor = NSColor.controlAccentColor.cgColor
            borderLayer.strokeColor = NSColor.controlAccentColor.cgColor
            layer.opacity = 1.0
            
        case .disabled:
            backgroundLayer.fillColor = NSColor.controlBackgroundColor.cgColor
            borderLayer.strokeColor = NSColor.disabledControlTextColor.cgColor
            layer.opacity = 0.5
            
        case .focused:
            backgroundLayer.fillColor = NSColor.controlBackgroundColor.cgColor
            borderLayer.strokeColor = NSColor.controlAccentColor.cgColor
            layer.opacity = 1.0
        }
    }
    
    // MARK: - State Management
    func getCurrentState() -> CAUIState {
        if !isEnabled {
            return .disabled
        } else if isSelected {
            return .selected
        } else if isHighlighted {
            return .highlighted
        } else {
            return .normal
        }
    }
    
    // MARK: - Animation
    func animateToState(_ state: CAUIState) {
        CATransaction.begin()
        CATransaction.setAnimationDuration(animationDuration)
        CATransaction.setAnimationTimingFunction(animationTimingFunction)
        
        updateAppearance()
        
        CATransaction.commit()
    }
    
    // MARK: - Mouse Events
    override func mouseEntered(with event: NSEvent) {
        super.mouseEntered(with: event)
        if isEnabled {
            isHighlighted = true
        }
    }
    
    override func mouseExited(with event: NSEvent) {
        super.mouseExited(with: event)
        isHighlighted = false
    }
    
    override func updateTrackingAreas() {
        super.updateTrackingAreas()
        
        for trackingArea in trackingAreas {
            removeTrackingArea(trackingArea)
        }
        
        let trackingArea = NSTrackingArea(
            rect: bounds,
            options: [.mouseEnteredAndExited, .activeInActiveApp],
            owner: self,
            userInfo: nil
        )
        addTrackingArea(trackingArea)
    }
}
//
//  CAEventHandler.swift
//  TestCAwigdet
//
//  Created by Core Animation UI Framework
//

import Cocoa
import QuartzCore

// MARK: - 事件类型枚举
enum CAEventType {
    case mouseDown
    case mouseUp
    case mouseDragged
    case mouseEntered
    case mouseExited
    case mouseMoved
    case keyDown
    case keyUp
    case scroll
    case gesture(NSGestureRecognizer)
}

// MARK: - 事件回调类型
typealias CAEventCallback = (CAEventType, NSEvent?) -> Void
typealias CAGestureCallback = (NSGestureRecognizer) -> Void

// MARK: - 事件处理器协议
protocol CAEventHandling {
    func handleEvent(_ type: CAEventType, event: NSEvent?)
    func addEventCallback(for type: CAEventType, callback: @escaping CAEventCallback)
    func removeEventCallback(for type: CAEventType)
}

// MARK: - 事件处理器类
class CAEventHandler: NSObject, CAEventHandling {
    
    // MARK: - Properties
    private var eventCallbacks: [String: CAEventCallback] = [:]
    private var gestureRecognizers: [NSGestureRecognizer] = []
    weak var targetView: NSView?
    
    // MARK: - Initialization
    init(targetView: NSView) {
        self.targetView = targetView
        super.init()
        setupEventHandling()
    }
    
    // MARK: - Setup
    private func setupEventHandling() {
        guard let view = targetView else { return }
        
        // 确保视图可以接收事件
        view.wantsLayer = true
        
        // 设置跟踪区域
        setupTrackingArea()
    }
    
    private func setupTrackingArea() {
        guard let view = targetView else { return }
        
        // 移除现有的跟踪区域
        for trackingArea in view.trackingAreas {
            view.removeTrackingArea(trackingArea)
        }
        
        // 创建新的跟踪区域
        let trackingArea = NSTrackingArea(
            rect: view.bounds,
            options: [
                .mouseEnteredAndExited,
                .mouseMoved,
                .activeInActiveApp,
                .inVisibleRect
            ],
            owner: self,
            userInfo: nil
        )
        view.addTrackingArea(trackingArea)
    }
    
    // MARK: - Event Handling
    func handleEvent(_ type: CAEventType, event: NSEvent?) {
        let key = type.callbackKey
        if let callback = eventCallbacks[key] {
            callback(type, event)
        }
    }
    
    func addEventCallback(for type: CAEventType, callback: @escaping CAEventCallback) {
        let key = type.callbackKey
        eventCallbacks[key] = callback
    }
    
    func removeEventCallback(for type: CAEventType) {
        let key = type.callbackKey
        eventCallbacks.removeValue(forKey: key)
    }
    
    // MARK: - Mouse Events
    func mouseDown(with event: NSEvent) {
        handleEvent(.mouseDown, event: event)
    }
    
    func mouseUp(with event: NSEvent) {
        handleEvent(.mouseUp, event: event)
    }
    
    func mouseDragged(with event: NSEvent) {
        handleEvent(.mouseDragged, event: event)
    }
    
    func mouseEntered(with event: NSEvent) {
        handleEvent(.mouseEntered, event: event)
    }
    
    func mouseExited(with event: NSEvent) {
        handleEvent(.mouseExited, event: event)
    }
    
    func mouseMoved(with event: NSEvent) {
        handleEvent(.mouseMoved, event: event)
    }
    
    // MARK: - Keyboard Events
    func keyDown(with event: NSEvent) {
        handleEvent(.keyDown, event: event)
    }
    
    func keyUp(with event: NSEvent) {
        handleEvent(.keyUp, event: event)
    }
    
    // MARK: - Scroll Events
    func scrollWheel(with event: NSEvent) {
        handleEvent(.scroll, event: event)
    }
    
    // MARK: - Gesture Recognition
    func addGestureRecognizer(_ gestureRecognizer: NSGestureRecognizer, callback: @escaping CAGestureCallback) {
        guard let view = targetView else { return }
        
        gestureRecognizer.target = self
        gestureRecognizer.action = #selector(handleGesture(_:))
        
        view.addGestureRecognizer(gestureRecognizer)
        gestureRecognizers.append(gestureRecognizer)
        
        // 存储回调
        let key = "gesture_\(gestureRecognizer.hash)"
        eventCallbacks[key] = { type, _ in
            if case .gesture(let recognizer) = type {
                callback(recognizer)
            }
        }
    }
    
    @objc private func handleGesture(_ gestureRecognizer: NSGestureRecognizer) {
        handleEvent(.gesture(gestureRecognizer), event: nil)
    }
    
    func removeGestureRecognizer(_ gestureRecognizer: NSGestureRecognizer) {
        guard let view = targetView else { return }
        
        view.removeGestureRecognizer(gestureRecognizer)
        gestureRecognizers.removeAll { $0 === gestureRecognizer }
        
        // 移除回调
        let key = "gesture_\(gestureRecognizer.hash)"
        eventCallbacks.removeValue(forKey: key)
    }
    
    // MARK: - Hit Testing
    func hitTest(point: NSPoint, in layer: CALayer) -> CALayer? {
        return layer.hitTest(point)
    }
    
    func convertPoint(_ point: NSPoint, from view: NSView?) -> NSPoint {
        guard let targetView = targetView else { return point }
        return targetView.convert(point, from: view)
    }
    
    func convertPoint(_ point: NSPoint, to view: NSView?) -> NSPoint {
        guard let targetView = targetView else { return point }
        return targetView.convert(point, to: view)
    }
    
    // MARK: - Layout Updates
    func updateTrackingArea() {
        setupTrackingArea()
    }
}

// MARK: - NSTrackingArea Delegate
// 注意：这些方法已经在主类中定义，这里不需要重复声明

// MARK: - Event Type Extensions
extension CAEventType {
    var callbackKey: String {
        switch self {
        case .mouseDown: return "mouseDown"
        case .mouseUp: return "mouseUp"
        case .mouseDragged: return "mouseDragged"
        case .mouseEntered: return "mouseEntered"
        case .mouseExited: return "mouseExited"
        case .mouseMoved: return "mouseMoved"
        case .keyDown: return "keyDown"
        case .keyUp: return "keyUp"
        case .scroll: return "scroll"
        case .gesture(let recognizer): return "gesture_\(recognizer.hash)"
        }
    }
}

// MARK: - 便捷方法
extension CAEventHandler {
    
    // 添加点击事件
    func onTap(_ callback: @escaping () -> Void) {
        addEventCallback(for: .mouseUp) { _, _ in
            callback()
        }
    }
    
    // 添加悬停事件
    func onHover(enter: @escaping () -> Void, exit: @escaping () -> Void) {
        addEventCallback(for: .mouseEntered) { _, _ in
            enter()
        }
        
        addEventCallback(for: .mouseExited) { _, _ in
            exit()
        }
    }
    
    // 添加拖拽事件
    func onDrag(_ callback: @escaping (NSPoint) -> Void) {
        addEventCallback(for: .mouseDragged) { _, event in
            if let event = event {
                let location = event.locationInWindow
                callback(location)
            }
        }
    }
    
    // 添加键盘事件
    func onKeyPress(_ callback: @escaping (String) -> Void) {
        addEventCallback(for: .keyDown) { _, event in
            if let event = event, let characters = event.characters {
                callback(characters)
            }
        }
    }
    
    // 添加滚动事件
    func onScroll(_ callback: @escaping (CGFloat, CGFloat) -> Void) {
        addEventCallback(for: .scroll) { _, event in
            if let event = event {
                callback(event.scrollingDeltaX, event.scrollingDeltaY)
            }
        }
    }
}
//
//  CAAnimationManager.swift
//  TestCAwigdet
//
//  Created by Core Animation UI Framework
//

import Cocoa
import QuartzCore

// MARK: - 动画类型枚举
enum CAAnimationType {
    case fadeIn
    case fadeOut
    case scaleUp
    case scaleDown
    case slideIn(direction: CASlideDirection)
    case slideOut(direction: CASlideDirection)
    case bounce
    case pulse
    case shake
    case rotate
    case flip
    case elastic
}

enum CASlideDirection {
    case left, right, up, down
}

// MARK: - 动画配置结构
struct CAAnimationConfig {
    var duration: TimeInterval = 0.25
    var delay: TimeInterval = 0.0
    var timingFunction: CAMediaTimingFunction = CAMediaTimingFunction(name: .easeInEaseOut)
    var repeatCount: Float = 0
    var autoreverses: Bool = false
    var fillMode: CAMediaTimingFillMode = .forwards
    var isRemovedOnCompletion: Bool = true
}

// MARK: - 动画管理器
class CAAnimationManager {
    
    static let shared = CAAnimationManager()
    
    private init() {}
    
    // MARK: - 基础动画方法
    func animate(layer: CALayer, 
                type: CAAnimationType, 
                config: CAAnimationConfig = CAAnimationConfig(),
                completion: (() -> Void)? = nil) {
        
        let animation = createAnimation(for: type, config: config)
        
        if let completion = completion {
            CATransaction.begin()
            CATransaction.setCompletionBlock(completion)
            layer.add(animation, forKey: type.animationKey)
            CATransaction.commit()
        } else {
            layer.add(animation, forKey: type.animationKey)
        }
    }
    
    // MARK: - 创建具体动画
    private func createAnimation(for type: CAAnimationType, config: CAAnimationConfig) -> CAAnimation {
        switch type {
        case .fadeIn:
            return createFadeAnimation(from: 0.0, to: 1.0, config: config)
            
        case .fadeOut:
            return createFadeAnimation(from: 1.0, to: 0.0, config: config)
            
        case .scaleUp:
            return createScaleAnimation(from: 0.8, to: 1.0, config: config)
            
        case .scaleDown:
            return createScaleAnimation(from: 1.0, to: 0.8, config: config)
            
        case .slideIn(let direction):
            return createSlideAnimation(direction: direction, isSlideIn: true, config: config)
            
        case .slideOut(let direction):
            return createSlideAnimation(direction: direction, isSlideIn: false, config: config)
            
        case .bounce:
            return createBounceAnimation(config: config)
            
        case .pulse:
            return createPulseAnimation(config: config)
            
        case .shake:
            return createShakeAnimation(config: config)
            
        case .rotate:
            return createRotateAnimation(config: config)
            
        case .flip:
            return createFlipAnimation(config: config)
            
        case .elastic:
            return createElasticAnimation(config: config)
        }
    }
    
    // MARK: - 具体动画实现
    private func createFadeAnimation(from: CGFloat, to: CGFloat, config: CAAnimationConfig) -> CABasicAnimation {
        let animation = CABasicAnimation(keyPath: "opacity")
        animation.fromValue = from
        animation.toValue = to
        animation.duration = config.duration
        animation.beginTime = CACurrentMediaTime() + config.delay
        animation.timingFunction = config.timingFunction
        animation.repeatCount = config.repeatCount
        animation.autoreverses = config.autoreverses
        animation.fillMode = config.fillMode
        animation.isRemovedOnCompletion = config.isRemovedOnCompletion
        return animation
    }
    
    private func createScaleAnimation(from: CGFloat, to: CGFloat, config: CAAnimationConfig) -> CABasicAnimation {
        let animation = CABasicAnimation(keyPath: "transform.scale")
        animation.fromValue = from
        animation.toValue = to
        animation.duration = config.duration
        animation.beginTime = CACurrentMediaTime() + config.delay
        animation.timingFunction = config.timingFunction
        animation.repeatCount = config.repeatCount
        animation.autoreverses = config.autoreverses
        animation.fillMode = config.fillMode
        animation.isRemovedOnCompletion = config.isRemovedOnCompletion
        return animation
    }
    
    private func createSlideAnimation(direction: CASlideDirection, isSlideIn: Bool, config: CAAnimationConfig) -> CABasicAnimation {
        let animation = CABasicAnimation(keyPath: "transform.translation")
        
        var fromValue: CGPoint
        var toValue: CGPoint
        
        let distance: CGFloat = 100.0
        
        switch direction {
        case .left:
            fromValue = isSlideIn ? CGPoint(x: -distance, y: 0) : CGPoint(x: 0, y: 0)
            toValue = isSlideIn ? CGPoint(x: 0, y: 0) : CGPoint(x: -distance, y: 0)
        case .right:
            fromValue = isSlideIn ? CGPoint(x: distance, y: 0) : CGPoint(x: 0, y: 0)
            toValue = isSlideIn ? CGPoint(x: 0, y: 0) : CGPoint(x: distance, y: 0)
        case .up:
            fromValue = isSlideIn ? CGPoint(x: 0, y: distance) : CGPoint(x: 0, y: 0)
            toValue = isSlideIn ? CGPoint(x: 0, y: 0) : CGPoint(x: 0, y: distance)
        case .down:
            fromValue = isSlideIn ? CGPoint(x: 0, y: -distance) : CGPoint(x: 0, y: 0)
            toValue = isSlideIn ? CGPoint(x: 0, y: 0) : CGPoint(x: 0, y: -distance)
        }
        
        animation.fromValue = NSValue(point: fromValue)
        animation.toValue = NSValue(point: toValue)
        animation.duration = config.duration
        animation.beginTime = CACurrentMediaTime() + config.delay
        animation.timingFunction = config.timingFunction
        animation.fillMode = config.fillMode
        animation.isRemovedOnCompletion = config.isRemovedOnCompletion
        return animation
    }
    
    private func createBounceAnimation(config: CAAnimationConfig) -> CAKeyframeAnimation {
        let animation = CAKeyframeAnimation(keyPath: "transform.scale")
        animation.values = [1.0, 1.2, 0.9, 1.1, 0.95, 1.0]
        animation.keyTimes = [0.0, 0.2, 0.4, 0.6, 0.8, 1.0]
        animation.duration = config.duration
        animation.beginTime = CACurrentMediaTime() + config.delay
        animation.timingFunction = config.timingFunction
        animation.repeatCount = config.repeatCount
        animation.autoreverses = config.autoreverses
        animation.fillMode = config.fillMode
        animation.isRemovedOnCompletion = config.isRemovedOnCompletion
        return animation
    }
    
    private func createPulseAnimation(config: CAAnimationConfig) -> CABasicAnimation {
        let animation = CABasicAnimation(keyPath: "transform.scale")
        animation.fromValue = 1.0
        animation.toValue = 1.1
        animation.duration = config.duration
        animation.beginTime = CACurrentMediaTime() + config.delay
        animation.timingFunction = config.timingFunction
        animation.repeatCount = Float.infinity
        animation.autoreverses = true
        animation.fillMode = config.fillMode
        animation.isRemovedOnCompletion = config.isRemovedOnCompletion
        return animation
    }
    
    private func createShakeAnimation(config: CAAnimationConfig) -> CAKeyframeAnimation {
        let animation = CAKeyframeAnimation(keyPath: "transform.translation.x")
        animation.values = [0, -10, 10, -8, 8, -5, 5, 0]
        animation.keyTimes = [0.0, 0.125, 0.25, 0.375, 0.5, 0.625, 0.75, 1.0]
        animation.duration = config.duration
        animation.beginTime = CACurrentMediaTime() + config.delay
        animation.repeatCount = config.repeatCount
        animation.fillMode = config.fillMode
        animation.isRemovedOnCompletion = config.isRemovedOnCompletion
        return animation
    }
    
    private func createRotateAnimation(config: CAAnimationConfig) -> CABasicAnimation {
        let animation = CABasicAnimation(keyPath: "transform.rotation.z")
        animation.fromValue = 0
        animation.toValue = 2 * Double.pi
        animation.duration = config.duration
        animation.beginTime = CACurrentMediaTime() + config.delay
        animation.timingFunction = config.timingFunction
        animation.repeatCount = config.repeatCount
        animation.fillMode = config.fillMode
        animation.isRemovedOnCompletion = config.isRemovedOnCompletion
        return animation
    }
    
    private func createFlipAnimation(config: CAAnimationConfig) -> CABasicAnimation {
        let animation = CABasicAnimation(keyPath: "transform.rotation.y")
        animation.fromValue = 0
        animation.toValue = Double.pi
        animation.duration = config.duration
        animation.beginTime = CACurrentMediaTime() + config.delay
        animation.timingFunction = config.timingFunction
        animation.repeatCount = config.repeatCount
        animation.fillMode = config.fillMode
        animation.isRemovedOnCompletion = config.isRemovedOnCompletion
        return animation
    }
    
    private func createElasticAnimation(config: CAAnimationConfig) -> CAKeyframeAnimation {
        let animation = CAKeyframeAnimation(keyPath: "transform.scale")
        animation.values = [1.0, 1.4, 0.9, 1.15, 0.95, 1.02, 1.0]
        animation.keyTimes = [0.0, 0.15, 0.3, 0.5, 0.65, 0.8, 1.0]
        animation.duration = config.duration
        animation.beginTime = CACurrentMediaTime() + config.delay
        animation.timingFunction = CAMediaTimingFunction(controlPoints: 0.68, -0.55, 0.265, 1.55)
        animation.repeatCount = config.repeatCount
        animation.fillMode = config.fillMode
        animation.isRemovedOnCompletion = config.isRemovedOnCompletion
        return animation
    }
    
    // MARK: - 停止动画
    func stopAnimation(layer: CALayer, type: CAAnimationType) {
        layer.removeAnimation(forKey: type.animationKey)
    }
    
    func stopAllAnimations(layer: CALayer) {
        layer.removeAllAnimations()
    }
}

// MARK: - 动画类型扩展
extension CAAnimationType {
    var animationKey: String {
        switch self {
        case .fadeIn: return "fadeIn"
        case .fadeOut: return "fadeOut"
        case .scaleUp: return "scaleUp"
        case .scaleDown: return "scaleDown"
        case .slideIn: return "slideIn"
        case .slideOut: return "slideOut"
        case .bounce: return "bounce"
        case .pulse: return "pulse"
        case .shake: return "shake"
        case .rotate: return "rotate"
        case .flip: return "flip"
        case .elastic: return "elastic"
        }
    }
}
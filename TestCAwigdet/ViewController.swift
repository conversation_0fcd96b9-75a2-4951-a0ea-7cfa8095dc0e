//
//  ViewController.swift
//  TestCAwigdet
//
//  Created by 王雷 on 10/6/25.
//

import Cocoa

class ViewController: NSViewController {

    // MARK: - UI Elements
    private var tabView: NSTabView!

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupTabs()
    }

    // MARK: - UI Setup
    private func setupUI() {
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.windowBackgroundColor.cgColor

        // 创建标签视图
        tabView = NSTabView()
        tabView.translatesAutoresizingMaskIntoConstraints = false
        tabView.tabViewType = .topTabsBezelBorder
        view.addSubview(tabView)

        // 设置约束
        NSLayoutConstraint.activate([
            tabView.topAnchor.constraint(equalTo: view.topAnchor),
            tabView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tabView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            tabView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }

    private func setupTabs() {
        // 按钮演示标签
        let buttonDemo = ButtonDemo()
        let buttonTabItem = NSTabViewItem(viewController: buttonDemo)
        buttonTabItem.label = "按钮组件"
        tabView.addTabViewItem(buttonTabItem)

        // 预留其他组件标签
        let placeholderVC = createPlaceholderViewController(title: "文本框组件", subtitle: "CATextField - 即将推出")
        let textFieldTabItem = NSTabViewItem(viewController: placeholderVC)
        textFieldTabItem.label = "文本框组件"
        tabView.addTabViewItem(textFieldTabItem)

        let listPlaceholderVC = createPlaceholderViewController(title: "列表组件", subtitle: "CAListView - 即将推出")
        let listTabItem = NSTabViewItem(viewController: listPlaceholderVC)
        listTabItem.label = "列表组件"
        tabView.addTabViewItem(listTabItem)
    }

    private func createPlaceholderViewController(title: String, subtitle: String) -> NSViewController {
        let vc = NSViewController()
        vc.view = NSView()
        vc.view.wantsLayer = true
        vc.view.layer?.backgroundColor = NSColor.windowBackgroundColor.cgColor

        let stackView = NSStackView()
        stackView.translatesAutoresizingMaskIntoConstraints = false
        stackView.orientation = .vertical
        stackView.spacing = 10
        stackView.alignment = .centerX
        vc.view.addSubview(stackView)

        let titleLabel = NSTextField(labelWithString: title)
        titleLabel.font = NSFont.systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = NSColor.labelColor
        titleLabel.alignment = .center

        let subtitleLabel = NSTextField(labelWithString: subtitle)
        subtitleLabel.font = NSFont.systemFont(ofSize: 16, weight: .regular)
        subtitleLabel.textColor = NSColor.secondaryLabelColor
        subtitleLabel.alignment = .center

        stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(subtitleLabel)

        NSLayoutConstraint.activate([
            stackView.centerXAnchor.constraint(equalTo: vc.view.centerXAnchor),
            stackView.centerYAnchor.constraint(equalTo: vc.view.centerYAnchor)
        ])

        return vc
    }

    override var representedObject: Any? {
        didSet {
        // Update the view, if already loaded.
        }
    }
}


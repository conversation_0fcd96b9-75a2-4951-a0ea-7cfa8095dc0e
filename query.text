# (已有 import ...)
from http.client import HTTPException
import json # <--- 确保 json 已导入
import os
import queue
import re
import io
import base64
import threading
import time
import traceback
from datetime import datetime, timedelta
# from venv import logger # 确保 venv 已正确安装，或者替换为 logging
from flask import Flask, Response, g, request, jsonify, send_file
import psutil
import pymysql
import pymysql.cursors # <--- 新增导入 DictCursor
from dateutil import parser
from werkzeug.utils import secure_filename
from config import Config
# monitor_control_service 不再需要导入 authenticate，因为它在旧代码中未被使用
# from monitor_control_service import authenticate

app = Flask(__name__)


# 4. ===== 定义 Flask 请求钩子 (用于日志记录) =====
# 这些钟会在每个请求前后自动执行

@app.before_request
def before_log():
    """在每个请求处理开始前执行。"""
    # 使用 Flask 的 g 对象来存储请求期间的数据
    g.start_time = time.time() # 记录请求开始时间
    # 初始化日志条目字典
    g.log_entry = {
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'method': request.method,
        'url': request.url,
        'remote_addr': request.remote_addr, # 记录请求来源 IP
        'headers': dict(request.headers),   # 记录请求头 (可选，可能包含敏感信息)
        'query_params': request.args.to_dict(),
        'json_body': request.json if request.is_json else None, # 记录 JSON 请求体
        'form_data': request.form.to_dict() if request.form else None, # 记录表单数据 (可选)
        'status_code': None, # 请求结束后填充
        'error_trace': None, # 如果发生错误则填充
        'duration_ms': None  # 请求结束后计算填充
    }
    # print(f"[DEBUG 5200] before_request: Initialized log entry for {request.path}")

@app.after_request
def after_log(response):
    """
    在每个请求成功处理后 (没有未捕获的 Python 异常) 执行。
    用于记录响应状态、计算耗时，并调用公共日志记录器。
    """
    # 确保 before_request 中的 g.log_entry 确实被创建了
    if hasattr(g, 'log_entry') and g.log_entry is not None:
        # 记录响应状态码
        g.log_entry['status_code'] = response.status_code

        # 计算请求处理耗时
        if hasattr(g, 'start_time') and g.start_time is not None:
             g.log_entry['duration_ms'] = round((time.time() - g.start_time) * 1000, 2)
        else:
             g.log_entry['duration_ms'] = -1 # 表示开始时间未记录

        # --- 调用公共 Redis 日志记录器 ---
        try:
            # 调用 redis_logger.py 中的 publish_log 函数
            # 传入收集到的日志信息 (g.log_entry) 和本应用的端口号 (5100 - 修正端口号)
            # publish_log(g.log_entry, 5100) # <--- 修正端口号 - 暂时注释掉，因为函数未定义
            # print(f"[DEBUG 5100] after_request: Called publish_log for {request.path}")
            pass # 添加 pass 避免 IndentationError
        except Exception as e:
            # 捕获调用 publish_log 时可能发生的意外错误 (虽然 publish_log 内部有处理)
            print(f"[ERROR 5100] Exception calling publish_log in after_request: {e}") # <--- 修正端口号
            traceback.print_exc()

    # 必须返回原始的 response 对象
    return response

@app.teardown_request
def teardown_log(exc):
    """
    在每个请求结束后执行，无论是否发生异常。
    如果发生了 Python 异常 (exc 不为 None)，则记录异常信息并调用公共日志记录器。
    """
    # exc 参数是请求处理期间发生的未捕获异常，如果没有异常则为 None
    if exc is not None:
        # 确保 g.log_entry 存在
        if hasattr(g, 'log_entry') and g.log_entry is not None:
            # 记录错误堆栈信息
            g.log_entry['error_trace'] = traceback.format_exc()
            # 通常服务器内部错误状态码是 500
            g.log_entry['status_code'] = 500

            # 仍然尝试计算耗时
            if hasattr(g, 'start_time') and g.start_time is not None:
                g.log_entry['duration_ms'] = round((time.time() - g.start_time) * 1000, 2)
            else:
                g.log_entry['duration_ms'] = -1

            # --- 调用公共 Redis 日志记录器记录异常 ---
            try:
                # publish_log(g.log_entry, 5100) # <--- 修正端口号 - 暂时注释掉
                # print(f"[DEBUG 5100] teardown_request: Called publish_log after exception for {request.path}")
                pass # 添加 pass 避免 IndentationError
            except Exception as log_e:
                print(f"[ERROR 5100] Exception calling publish_log in teardown_request (after error): {log_e}") # <--- 修正端口号
                traceback.print_exc()
        else:
            # 如果连 g.log_entry 都没有，直接打印错误
             print(f"[ERROR 5100] Exception occurred but g.log_entry not found: {exc}") # <--- 修正端口号
             traceback.print_exc()

    # 这个钩子不需要返回任何东西
    # print(f"[DEBUG 5100] teardown_request: Finished for {request.path}. Exception was: {exc}")


# 加载配置到 app.config
app.config.from_object(Config)

# --- 删除旧的 DB_CONFIG 和 connect_db() ---
# DB_CONFIG = { ... }
# def connect_db(): ...

# --- 定义新的数据库连接函数 (使用 DictCursor) ---
def connect_read_db():
    """连接到只读数据库实例"""
    return pymysql.connect(
        host=app.config['DB_READ_HOST'],
        port=app.config['DB_READ_PORT'],
        user=app.config['DB_READ_USER'],
        password=app.config['DB_READ_PASSWORD'],
        database=app.config['DB_READ_NAME'], # 连接时指定数据库名
        charset='utf8mb4',
        connect_timeout=10,
        read_timeout=10,
        cursorclass=pymysql.cursors.DictCursor # <--- 使用 DictCursor
    )

def connect_write_db():
    """连接到读写数据库实例"""
    return pymysql.connect(
        host=app.config['DB_WRITE_HOST'],
        port=app.config['DB_WRITE_PORT'],
        user=app.config['DB_WRITE_USER'],
        password=app.config['DB_WRITE_PASSWORD'],
        database=app.config['DB_WRITE_NAME'], # 连接时指定数据库名
        charset='utf8mb4',
        connect_timeout=10,
        read_timeout=10,
        cursorclass=pymysql.cursors.DictCursor # <--- 使用 DictCursor
    )

# --- 重新定义认证函数 (需要从 monitor_control_service.py 复制过来或重新实现) ---
# 注意：旧代码导入了 authenticate 但未使用，这里根据需要决定是否保留

# 标记哪些路由连接到了数据库
def connects_to_db(func):
    func.connects_to_db = True
    return func


##############################################
#   1. SSE 客户端队列管理
##############################################
clients = []

class ClientQueue:
    def __init__(self):
        self.queue = queue.Queue()
    
    def put(self, data):
        self.queue.put(data)
    
    def get(self, timeout=3600):
        return self.queue.get(timeout=timeout)

def broadcast_log(entry):
    """
    将日志条目推送到所有客户端队列，并打印调试信息
    """
    #print("\n[DEBUG] broadcast_log => ", entry)  # 调试打印
    for cq in clients:
        cq.put(entry)

def broadcast_stats(stats_data):
    """
    将系统统计数据推送到所有SSE客户端，并打印调试信息
    """
    #print("\n[DEBUG] broadcast_stats => ", stats_data)  # 调试打印
    for cq in clients:
        cq.put(stats_data)

@app.errorhandler(HTTPException)
def handle_http_exception(e):
    """
    捕获404, 400, 405等HTTP异常
    """
    if hasattr(g, 'log_entry'):
        g.log_entry['error_trace'] = f"HTTPException: {e.description} (code={e.code})"
        g.log_entry['status_code'] = e.code
        if hasattr(g, 'start_time'): # 检查 start_time 是否存在
             g.log_entry['duration_ms'] = round((time.time() - g.start_time) * 1000, 2)
        # broadcast_log(g.log_entry) # 考虑是否在 HTTP 错误时广播日志
    return jsonify({'error': e.description, 'code': e.code}), e.code


##############################################
#   3. SSE 路由：/stream_logs
##############################################
@app.route('/stream_logs', methods=['GET'])
def stream_logs():
    """
    SSE Endpoint：前端会连接这里，保持长连接，用以实时接收日志事件
    """
    cq = ClientQueue()
    clients.append(cq)

    def event_stream():
        try:
            while True:
                entry = cq.get(timeout=3600)
                data_str = "data: " + json.dumps(entry, ensure_ascii=False) + "\n\n"
                yield data_str
        except queue.Empty: # 更精确地捕获超时
            pass # 超时是正常的，继续循环
        except Exception as e:
             print(f"[ERROR 5100] Exception in SSE event_stream: {e}")
             traceback.print_exc()
        finally:
            if cq in clients:
                clients.remove(cq)
                print(f"[INFO 5100] SSE client disconnected. Remaining clients: {len(clients)}")


    print(f"[INFO 5100] New SSE client connected. Total clients: {len(clients)}")
    return Response(event_stream(), mimetype='text/event-stream', headers={'X-Accel-Buffering': 'no'})


##############################################
#   4. 后台线程：定期采集系统状态并 SSE 推送
##############################################
def system_stats_loop():
    """
    后台线程，周期性收集 CPU、内存、以及前10进程的使用情况并广播。
    这里已包含 cmdline 字段，可用于查看 Python 进程运行的脚本等信息。
    """
    while True:
        try:
            cpu = psutil.cpu_percent(interval=None)
            mem = psutil.virtual_memory()
            mem_percent = mem.percent

            processes = []
            # 在 attrs 里保留对 pid、name、cmdline、cpu_percent 的快速获取
            for p in psutil.process_iter(attrs=['pid', 'name', 'cmdline', 'cpu_percent']):
                try:
                    # 获取内存占用
                    mem_info = p.memory_info()  # rss, vms, etc.
                    mem_mb = round(mem_info.rss / 1024 / 1024, 1)

                    # 获取可执行文件路径
                    try:
                        exe_path = p.exe()
                    except psutil.AccessDenied:
                        exe_path = "(access denied)"
                    except psutil.Error: # 更通用的 psutil 错误
                        exe_path = "(psutil error)"
                    except Exception: # 其他未知错误
                        exe_path = "(unknown)"


                    # 检查 p.info 是否包含所需键
                    pid_val = p.info.get('pid')
                    if pid_val is None: continue # 跳过没有 pid 的进程信息

                    info = {
                        'pid': pid_val,
                        'name': p.info.get('name') or "",
                        'cpu_percent': p.info.get('cpu_percent', 0.0),
                        'cmdline': p.info.get('cmdline', []),
                        'mem_mb': mem_mb,
                        'exe_path': exe_path,
                        'service_port': None
                    }
                    processes.append(info)

                except psutil.NoSuchProcess:
                    pass
                except psutil.AccessDenied:
                    pass
                except Exception as iter_ex:
                     # 添加日志记录单个进程处理错误
                     print(f"[ERROR 5100] Error processing process {p.info.get('pid', 'N/A')}: {iter_ex}")


            # 按 CPU 使用率降序排序，取前10
            processes.sort(key=lambda x: x.get('cpu_percent', 0.0), reverse=True) # 使用 .get 以防万一
            top10 = processes[:10]

            data = {
                "type": "stats",
                "cpu_percent": cpu,
                "mem_percent": mem_percent,
                "top10_processes": top10
            }

            # 将系统状态推送给所有 SSE 客户端
            broadcast_stats(data)

        except Exception as e:
            print("[ERROR 5100] Unhandled exception in system_stats_loop =>", e)
            traceback.print_exc()

        time.sleep(5)


########################
#   新增：结束进程接口
########################
@app.route('/kill_process', methods=['POST'])
def kill_process():
    # if not authenticate(request): # <-- 暂时注释掉认证
    #     return jsonify({'error': 'Unauthorized'}), 401
    
    data = request.get_json()
    pid = data.get('pid')
    if not pid:
        return jsonify({'error': 'Missing pid'}), 400
    
    try:
        p = psutil.Process(pid)
        p.kill()
        return jsonify({'message': f'Process {pid} killed successfully.'}), 200
    except psutil.NoSuchProcess:
        return jsonify({'error': f'Process {pid} not found'}), 404
    except Exception as e:
        print(f"[ERROR 5100] Error killing process {pid}: {e}") # 添加日志
        traceback.print_exc()
        return jsonify({'error': str(e)}), 500

# ================================================================
#   下面是修改后的路由，使用了新的连接函数和 DictCursor 结果处理
# ================================================================

# 用于监视程序启动和网络切换的
def ensure_table_exists(cursor):
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS iosCalendar (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task TEXT CHARACTER SET utf8mb4 NOT NULL,
        status VARCHAR(20) CHARACTER SET utf8mb4 NOT NULL,
        date_added VARCHAR(50),
        date_completed VARCHAR(50),
        server_finishjob_date VARCHAR(50)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    """ # 添加 Engine 和 Collate
    cursor.execute(create_table_sql)

#用于绑定更多想法
def ensure_bindmoredetails_exists(cursor):
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS bindmoredetails (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT NOT NULL,
        additional_thoughts TEXT CHARACTER SET utf8mb4,
        date_added VARCHAR(50) CHARACTER SET utf8mb4
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    """ # 添加 Engine 和 Collate
    cursor.execute(create_table_sql)

# 确保文件存储表存在
def ensure_file_storage_table_exists(cursor):
    sql = """
    CREATE TABLE IF NOT EXISTS file_storage (
        id INT AUTO_INCREMENT PRIMARY KEY,
        file_name VARCHAR(255) CHARACTER SET utf8mb4 NOT NULL,
        date_added DATETIME,
        file_content LONGBLOB,
        file_size BIGINT,
        download_latest_time DATETIME NULL
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    """
    cursor.execute(sql)

# 确保 SlotFileMapping 表存在
def ensure_slot_file_mapping_exists(cursor):
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS slotfilemapping (
        id INT AUTO_INCREMENT PRIMARY KEY,
        slot_id VARCHAR(36) NOT NULL,
        file_name VARCHAR(255) NULL,
        file_id INT NULL, -- 假设 file_id 是 file_storage 表的外键
        level INT NULL,
        slot_index INT NULL,
        UNIQUE KEY unique_slot_id (slot_id)
        -- FOREIGN KEY (file_id) REFERENCES file_storage(id) ON DELETE SET NULL -- 可选外键约束
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    """
    cursor.execute(create_table_sql)

# 确保 printer_record 表存在
def ensure_printer_record_table_exists(cursor):
    create_table_sql = """
    CREATE TABLE IF NOT EXISTS printer_record (
        id INT AUTO_INCREMENT PRIMARY KEY,
        task_id INT,
        task_name TEXT CHARACTER SET utf8mb4,
        gpt_response MEDIUMTEXT CHARACTER SET utf8mb4,
        usage_type TEXT CHARACTER SET utf8mb4,
        save_date VARCHAR(50), -- 保持 VARCHAR
        file_data LONGBLOB,
        file_path TEXT CHARACTER SET utf8mb4,
        gpt_response_type TEXT CHARACTER SET utf8mb4
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
    """
    cursor.execute(create_table_sql)

# 确保 ios_app_behavior_monitoring 表存在
def ensure_ios_app_behavior_table_exists(cursor):
     sql = """
         CREATE TABLE IF NOT EXISTS ios_app_behavior_monitoring (
             id INT AUTO_INCREMENT PRIMARY KEY,
             start_time VARCHAR(255),
             network_type_before VARCHAR(255),
             network_type_after VARCHAR(255),
             network_change_time VARCHAR(255),
             network_change_frequency VARCHAR(255),
             last_page_visited VARCHAR(255),
             current_page_visited VARCHAR(255),
             user_action VARCHAR(255),
             action_time VARCHAR(255),
             app_state VARCHAR(255),
             error_message TEXT CHARACTER SET utf8mb4, -- 改为 TEXT
             session_duration VARCHAR(255),
             task_id VARCHAR(255),
             gps_location VARCHAR(255),
             battery_level VARCHAR(255),
             memory_usage VARCHAR(255),
             device_time VARCHAR(255)
         ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
     """
     cursor.execute(sql)

# Ping 路由
@app.route('/ping', methods=['GET'])
def ping():
    return jsonify({"status": "pong", "timestamp": time.time()})

# 保存插槽与文件名的映射
@app.route('/saveSlotFileMapping', methods=['POST'])
@connects_to_db
def save_slot_file_mapping():
    data = request.json
    id_val = data.get('id') # Use different variable name from built-in id
    file_name = data.get('file_name')
    file_id = data.get('file_id')
    level = data.get('level')
    slot_index = data.get('slot_index')

    if not id_val:
        return jsonify({"error": "Missing id"}), 400

    connection = None
    try:
        connection = connect_write_db() # <--- 使用写连接
        with connection.cursor() as cursor:
            ensure_slot_file_mapping_exists(cursor) # 确保表存在

            if not file_id:
                sql_remove_file_mapping = "UPDATE slotfilemapping SET file_name = NULL, file_id = NULL WHERE id = %s"
                cursor.execute(sql_remove_file_mapping, (id_val,))
            else:
                sql_insert_or_update = """
                    INSERT INTO slotfilemapping (id, file_name, file_id, level, slot_index)
                    VALUES (%s, %s, %s, %s, %s)
                    ON DUPLICATE KEY UPDATE
                    file_name = VALUES(file_name),
                    file_id = VALUES(file_id),
                    level = VALUES(level),
                    slot_index = VALUES(slot_index)
                """
                cursor.execute(sql_insert_or_update, (id_val, file_name, file_id, level, slot_index))

            connection.commit()
            return jsonify({"message": "slotfilemapping saved successfully"}), 201
    except Exception as e:
        print("Failed to save slotfilemapping:", e)
        traceback.print_exc()
        if connection: connection.rollback() # Check if connection exists before rollback
        return jsonify({"error": "Failed to save slotfilemapping"}), 500
    finally:
        if connection: connection.close()

# 加载所有的插槽与文件名的映射
@app.route('/loadSlotFileMappings', methods=['GET'])
@connects_to_db
def load_slot_file_mappings():
    connection = None
    try:
        connection = connect_read_db() # <--- 使用读连接
        with connection.cursor() as cursor:
            # ensure_slot_file_mapping_exists(cursor) # 在只读操作中通常不创建表
            sql_select_query = "SELECT id, file_name, file_id, level, slot_index FROM slotfilemapping"
            cursor.execute(sql_select_query)
            mappings = cursor.fetchall() # 返回字典列表
            # result = [{"id": mapping['id'], "file_name": mapping['file_name'], ...} for mapping in mappings] # 不需要转换
            return jsonify(mappings), 200
    except Exception as e:
        print("Failed to load slotfilemapping:", e)
        traceback.print_exc()
        return jsonify({"error": "Failed to load slotfilemapping"}), 500
    finally:
        if connection: connection.close()

# 添加想法
@app.route('/addThought', methods=['POST'])
@connects_to_db
def add_thought():
    data = request.json
    task_id = data.get('task_id')
    additional_thoughts = data.get('additional_thoughts')
    date_added = data.get('date_added')

    if not task_id or not additional_thoughts or not date_added:
        return jsonify({"error": "Missing data"}), 400

    connection = None
    try:
        connection = connect_write_db() # <--- 使用写连接
        with connection.cursor() as cursor:
            ensure_bindmoredetails_exists(cursor)
            sql_insert_query = "INSERT INTO bindmoredetails (task_id, additional_thoughts, date_added) VALUES (%s, %s, %s)"
            cursor.execute(sql_insert_query, (task_id, additional_thoughts, date_added))
            connection.commit()
            return jsonify({"message": "Thought added successfully"}), 201
    except Exception as e:
        print("Failed to insert into bindmoredetails:", e)
        traceback.print_exc()
        if connection: connection.rollback()
        return jsonify({"error": "Failed to insert thought"}), 500
    finally:
        if connection: connection.close()

# 获取想法
@app.route('/getThoughts', methods=['GET'])
@connects_to_db
def get_thoughts():
    task_id = request.args.get('task_id')
    if not task_id:
        return jsonify({"error": "Missing task_id"}), 400

    connection = None
    try:
        connection = connect_read_db() # <--- 使用读连接
        with connection.cursor() as cursor:
            sql_select_query = "SELECT id, additional_thoughts, date_added FROM bindmoredetails WHERE task_id = %s"
            cursor.execute(sql_select_query, (task_id,))
            thoughts = cursor.fetchall() # 返回字典列表

            # 处理可能的 None 值
            result = []
            for thought in thoughts:
                 result.append({
                     "id": thought['id'],
                     "additional_thoughts": thought['additional_thoughts'] if thought['additional_thoughts'] is not None else "无内容",
                     "date_added": thought['date_added'] if thought['date_added'] is not None else "未知日期"
                 })

            return jsonify(result), 200
    except Exception as e:
        print("Error getting thoughts:", e)
        traceback.print_exc()
        return jsonify({"error": str(e)}), 500
    finally:
        if connection: connection.close()

# 删除想法
@app.route('/deleteThought', methods=['DELETE'])
@connects_to_db
def delete_thought():
    thought_id = request.args.get('thought_id')
    if not thought_id:
        return jsonify({"error": "Missing thought_id"}), 400

    connection = None
    try:
        connection = connect_write_db() # <--- 使用写连接
        with connection.cursor() as cursor:
            # 检查是否存在
            sql_check_query = "SELECT id FROM bindmoredetails WHERE id = %s"
            cursor.execute(sql_check_query, (thought_id,))
            result = cursor.fetchone()
            if not result:
                return jsonify({"error": "Thought not found"}), 404

            # 删除
            sql_delete_query = "DELETE FROM bindmoredetails WHERE id = %s"
            cursor.execute(sql_delete_query, (thought_id,))
            connection.commit()
            return jsonify({"message": "Thought deleted successfully"}), 200
    except Exception as e:
        print("Error deleting thought:", e)
        traceback.print_exc()
        if connection: connection.rollback()
        return jsonify({"error": str(e)}), 500
    finally:
        if connection: connection.close()

# 获取最近任务
@app.route('/getRecentTasks', methods=['GET'])
@connects_to_db
def get_recent_tasks():
    connection = None
    try:
        days_interval = 2
        days_ago = parser.parse((datetime.now() - timedelta(days=days_interval)).strftime('%Y-%m-%d'))

        connection = connect_read_db() # <--- 使用读连接
        with connection.cursor() as cursor:
            # 1. 从 iosCalendar 获取所有任务
            sql_select_iosCalendar = "SELECT id, task, status, date_added, date_completed FROM iosCalendar ORDER BY id DESC"
            cursor.execute(sql_select_iosCalendar)
            calendar_tasks = cursor.fetchall() # 字典列表

            task_list = []
            added_task_ids = set()

            # 2. 按日期过滤 iosCalendar 任务
            for task in calendar_tasks:
                try:
                    task_date_str = task.get('date_added') # 使用 .get() 安全访问
                    if not task_date_str: continue
                    task_date = parser.parse(task_date_str).date()
                except (ValueError, TypeError, parser.ParserError):
                    continue

                if task_date >= days_ago.date():
                    task_list.append(task) # 直接添加字典
                    added_task_ids.add(task['id'])

            # 3. 从 bindmoredetails 获取最近的想法关联的 task_id
            sql_select_bindmoredetails = "SELECT task_id, date_added FROM bindmoredetails ORDER BY id DESC"
            cursor.execute(sql_select_bindmoredetails)
            additional_details = cursor.fetchall() # 字典列表

            recent_thought_task_ids = set()
            for detail in additional_details:
                try:
                    detail_date_str = detail.get('date_added')
                    if not detail_date_str: continue
                    detail_date = parser.parse(detail_date_str).date()
                except (ValueError, TypeError, parser.ParserError):
                    continue

                if detail_date >= days_ago.date():
                     task_id_val = detail.get('task_id')
                     if task_id_val: recent_thought_task_ids.add(task_id_val)


            # 4. 获取这些 task_id 对应的任务详情
            if recent_thought_task_ids:
                # 过滤掉已经添加的任务ID
                ids_to_fetch = tuple(recent_thought_task_ids - added_task_ids)

                if ids_to_fetch: # 只有当有需要获取的ID时才查询
                    sql_select_related_iosCalendar = "SELECT id, task, status, date_added, date_completed FROM iosCalendar WHERE id IN %s ORDER BY id DESC"
                    cursor.execute(sql_select_related_iosCalendar, (ids_to_fetch,))
                    related_tasks = cursor.fetchall() # 字典列表

                    # 5. 合并结果
                    for task in related_tasks:
                        if task['id'] not in added_task_ids: # 双重检查
                            task_list.append(task)
                            added_task_ids.add(task['id'])

            # 6. 格式化输出 (现在 task_list 已经是字典列表, 无需额外格式化)
            print(f"Returning {len(task_list)} recent tasks.")
            return jsonify(task_list), 200

    except Exception as e:
        print("Error occurred in get_recent_tasks:", e)
        traceback.print_exc()
        return jsonify({"error": f"An unexpected error occurred: {str(e)}"}), 500
    finally:
        if connection: connection.close()


# 搜索想法 (这个是出问题的路由)
@app.route('/searchThoughts', methods=['GET'])
@connects_to_db
def search_thoughts():
    keyword = request.args.get('keyword', default='', type=str)
    if not keyword:
        print("Debug: No keyword provided.")
        return jsonify({"error": "Missing keyword"}), 400

    keyword = keyword.replace(" ", "").lower()
    is_english = re.match("^[A-Za-z]+$", keyword)
    keywords = [keyword] if is_english else list(keyword)
    required_match_count = 1 if is_english else (2 if len(keywords) > 2 else 1)

    # 构建 SQL 条件语句
    thought_conditions = " OR ".join([f"LOWER(additional_thoughts) LIKE %s" for _ in keywords])
    task_conditions = " OR ".join([f"LOWER(task) LIKE %s" for _ in keywords])

    # 构建参数元组
    query_parameters = tuple(f"%{kw}%" for kw in keywords)

    connection = None
    final_result_list = [] # Initialize earlier
    try:
        connection = connect_read_db()
        print("Debug: /searchThoughts - Database connection established (Read DB).")
        with connection.cursor() as cursor:
            # UNION 查询优化：直接查 task_id
            sql_union_ids = f"""
            SELECT DISTINCT task_id
            FROM bindmoredetails
            WHERE {thought_conditions}
            UNION
            SELECT DISTINCT id AS task_id
            FROM iosCalendar
            WHERE {task_conditions}
            """
            union_params = query_parameters + query_parameters
            print(f"Debug: Executing UNION query: {sql_union_ids} with params: {union_params}")
            cursor.execute(sql_union_ids, union_params)
            potential_task_ids = {row['task_id'] for row in cursor.fetchall()}

            print(f"Debug: Found {len(potential_task_ids)} potential task IDs from UNION.")

            if not potential_task_ids:
                # If no potential tasks, return the initialized empty list immediately
                print(f"Debug: Returning {len(final_result_list)} final tasks (no potential matches).")
                return jsonify(final_result_list), 200

            # 获取这些 ID 对应的任务和想法，以便进行内容匹配
            task_ids_tuple = tuple(potential_task_ids)
            sql_fetch_tasks = "SELECT id, task FROM iosCalendar WHERE id IN %s"
            cursor.execute(sql_fetch_tasks, (task_ids_tuple,))
            task_data = {row['id']: row['task'] for row in cursor.fetchall()}
            sql_fetch_thoughts = "SELECT task_id, additional_thoughts FROM bindmoredetails WHERE task_id IN %s"
            cursor.execute(sql_fetch_thoughts, (task_ids_tuple,))
            thought_data = {}
            for row in cursor.fetchall():
                 task_id = row['task_id']
                 if task_id not in thought_data:
                     thought_data[task_id] = []
                 thought_data[task_id].append(row['additional_thoughts'])

            # 在内存中进行匹配过滤
            filtered_task_ids = set()
            for task_id in potential_task_ids:
                task_text = task_data.get(task_id, '').lower()
                task_match_count = sum(1 for kw in keywords if kw in task_text)
                thought_texts = thought_data.get(task_id, [])
                thought_match_count = 0
                for thought in thought_texts:
                     thought_match_count += sum(1 for kw in keywords if kw in thought.lower())
                if task_match_count > 0 or thought_match_count > 0:
                     filtered_task_ids.add(task_id)

            print(f"Debug: Found {len(filtered_task_ids)} task IDs after content filtering.")

            # Only proceed if there are filtered task IDs
            if filtered_task_ids:
                # 获取最终任务详情
                final_task_ids_tuple = tuple(filtered_task_ids)
                sql_final_tasks = "SELECT id, task, status, date_added, date_completed FROM iosCalendar WHERE id IN %s ORDER BY date_added DESC"
                cursor.execute(sql_final_tasks, (final_task_ids_tuple,))
                final_tasks = cursor.fetchall()

                # 组装结果，添加想法
                result_dict = {task['id']: task for task in final_tasks}
                for task_id, thoughts in thought_data.items():
                     if task_id in result_dict:
                         result_dict[task_id]['additional_thoughts'] = thoughts

                # 确保所有任务都有 additional_thoughts 键
                final_result_list = list(result_dict.values()) # Assignment happens here
                for task in final_result_list:
                     if 'additional_thoughts' not in task:
                         task['additional_thoughts'] = []
            # else: # If no tasks passed filtering, final_result_list remains []

        # This print and return will now always work because final_result_list is initialized
        print(f"Debug: Returning {len(final_result_list)} final tasks.")
        return jsonify(final_result_list), 200

    except Exception as e:
        print("Error occurred in /searchThoughts:", e)
        traceback.print_exc()
        # Ensure final_result_list isn't returned on error, return error message instead
        return jsonify({"error": str(e)}), 500
    finally:
        if connection: connection.close()
        print("Debug: /searchThoughts - Database connection closed.")


# --- 文件操作路由 ---
# 查询单个文件 (保持 DictCursor 兼容)
@app.route('/getFile', methods=['GET'])
@connects_to_db
def get_file():
    file_id = request.args.get('file_id')
    if not file_id: return jsonify({"error": "Missing file_id"}), 400
    connection = None
    try:
        connection = connect_read_db() # <--- 读连接
        with connection.cursor() as cursor:
            ensure_file_storage_table_exists(cursor) # 确保表存在（只读模式下可能不需要）
            sql_select_query = "SELECT file_name, file_content FROM file_storage WHERE id = %s"
            cursor.execute(sql_select_query, (file_id,))
            result = cursor.fetchone() # 返回字典

            if not result:
                return jsonify({"error": "File not found"}), 404

            file_name = result['file_name'] # 使用字典键
            file_content = result['file_content'] # 使用字典键
            return send_file(io.BytesIO(file_content), as_attachment=True, download_name=file_name)
    except Exception as e:
        print("Error getting file:", e); traceback.print_exc()
        return jsonify({"error": str(e)}), 500
    finally:
        if connection: connection.close()

# 获取所有文件列表 (保持 DictCursor 兼容)
@app.route('/getFiles', methods=['GET'])
@connects_to_db
def get_files():
    connection = None
    try:
        connection = connect_read_db() # <--- 使用读连接
        with connection.cursor() as cursor:
            # ensure_file_storage_table_exists(cursor) # <--- 注释掉或删除这一行
            sql_select_query = "SELECT id, file_name, date_added, file_size, download_latest_time FROM file_storage"
            cursor.execute(sql_select_query) # <--- 执行 SELECT 查询
            files = cursor.fetchall() # 获取结果

            # 按 id 降序排序
            result = sorted(files, key=lambda x: x.get("id", 0), reverse=True)

            return jsonify(result), 200
    except Exception as e:
        print("Error getting files:", e); traceback.print_exc()
        return jsonify({"error": str(e)}), 500
    finally:
        if connection: connection.close()

# 允许的文件类型
def allowed_file(filename):
    allowed_extensions = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'zip', 'mov', 'mp4', 'heic', 'gz'} # 添加更多类型，支持 .gz 文件
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in allowed_extensions

# 上传文件
@app.route('/uploadFile', methods=['POST'])
@connects_to_db
def upload_file():
    if 'file' not in request.files:
        return jsonify({"error": "No file part in the request"}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({"error": "No selected file"}), 400

    if file and allowed_file(file.filename):
        connection = None
        try:
            # 直接使用原始文件名，不再调用 secure_filename
            original_filename = file.filename
            # 检查文件名长度是否超过数据库字段限制（如果有限制）
            MAX_FILENAME_LENGTH = 255 # 假设数据库字段长度限制为 255
            if len(original_filename) > MAX_FILENAME_LENGTH:
                return jsonify({"error": f"Filename exceeds maximum length of {MAX_FILENAME_LENGTH} characters"}), 400

            file_data = file.read()
            file_size = len(file_data)
            date_added = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

            connection = connect_write_db() # <--- 写连接
            with connection.cursor() as cursor:
                ensure_file_storage_table_exists(cursor)
                sql_insert_query = """
                    INSERT INTO file_storage (file_name, date_added, file_content, file_size)
                    VALUES (%s, %s, %s, %s)
                """
                cursor.execute(sql_insert_query, (original_filename, date_added, file_data, file_size))
            connection.commit()

            return jsonify({"message": "File uploaded successfully"}), 201
        except Exception as e:
            print("Failed to insert file into MySQL:", e); traceback.print_exc()
            if connection: connection.rollback()
            return jsonify({"error": "Failed to insert data"}), 500
        finally:
            if connection: connection.close()
    else:
        return jsonify({"error": "File type not allowed"}), 400

# 下载文件 (保持 DictCursor 兼容)
@app.route('/downloadFile', methods=['GET'])
@connects_to_db
def download_file():
    file_id = request.args.get('file_id')
    if not file_id: return jsonify({"error": "Missing file_id"}), 400

    connection = None
    try:
        connection = connect_write_db() # <--- 写连接 (因为要更新时间戳)
        with connection.cursor() as cursor:
            ensure_file_storage_table_exists(cursor)
            sql_select_query = "SELECT file_name, file_content FROM file_storage WHERE id = %s"
            cursor.execute(sql_select_query, (file_id,))
            file_record = cursor.fetchone() # 字典

            if not file_record:
                return jsonify({"error": "File not found"}), 404

            file_name = file_record['file_name'] # 字典键
            file_content = file_record['file_content'] # 字典键

            # 更新下载时间
            download_latest_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            sql_update_query = "UPDATE file_storage SET download_latest_time = %s WHERE id = %s"
            cursor.execute(sql_update_query, (download_latest_time, file_id))
            connection.commit()

            return send_file(
                io.BytesIO(file_content),
                download_name=file_name,
                as_attachment=True
            )

    except Exception as e:
        print(f"Error downloading file (id: {file_id}):", e); traceback.print_exc()
        if connection:
            try: connection.rollback()
            except Exception as rb_e: print(f"Error during rollback: {rb_e}")
        return jsonify({"error": f"An internal error occurred: {str(e)}"}), 500
    finally:
        if connection: connection.close()

# --- 任务相关路由 ---
@app.route('/uploadContacts', methods=['POST'])
@connects_to_db
def upload_contacts():
    data = request.json
    task = data.get('task')
    date_added = data.get('date_added')
    if task and date_added:
        connection = None
        try:
            connection = connect_write_db() # <--- 写连接
            with connection.cursor() as cursor:
                ensure_table_exists(cursor)
                sql = "INSERT INTO iosCalendar (task, status, date_added, server_finishjob_date) VALUES (%s, %s, %s, NOW())"
                cursor.execute(sql, (task, 'pending', date_added))
            connection.commit()
            return jsonify({"message": "Task added successfully"}), 200
        except Exception as e:
            print("Error adding task:", e); traceback.print_exc()
            if connection: connection.rollback()
            return jsonify({"error": str(e)}), 500
        finally:
            if connection: connection.close()
    return jsonify({"error": "Invalid data"}), 400

# 删除任务 (需要添加 @connects_to_db)
@app.route('/deleteTask', methods=['POST'])
@connects_to_db # <-- 添加装饰器
def delete_task():
    data = request.json
    task_id = data.get('task_id')

    # 验证 task_id (例如，确保是整数)
    try:
        task_id = int(task_id)
    except (ValueError, TypeError):
        return jsonify({"error": "Invalid task ID format"}), 400


    connection = None
    try:
        connection = connect_write_db() # <--- 写连接
        with connection.cursor() as cursor:
            # 先删除关联的想法
            sql_delete_thoughts = "DELETE FROM bindmoredetails WHERE task_id = %s"
            cursor.execute(sql_delete_thoughts, (task_id,))
            # 再删除任务本身
            sql_delete_task = "DELETE FROM iosCalendar WHERE id = %s"
            cursor.execute(sql_delete_task, (task_id,))
        connection.commit()
        return jsonify({"message": "Task and related thoughts deleted successfully"}), 200
    except Exception as e:
        print(f"Error deleting task {task_id}:", e); traceback.print_exc()
        if connection: connection.rollback()
        return jsonify({"error": str(e)}), 500
    finally:
        if connection: connection.close()

# 完成任务 (需要添加 @connects_to_db)
@app.route('/completeTask', methods=['POST'])
@connects_to_db # <-- 添加装饰器
def complete_task():
    data = request.json
    task_id = data.get('task_id')
    date_completed = data.get('date_completed')

    # 验证数据
    try:
        task_id = int(task_id)
        # 可以添加对 date_completed 格式的验证
        parser.parse(date_completed)
    except (ValueError, TypeError, parser.ParserError):
         return jsonify({"error": "Invalid data format"}), 400


    connection = None
    try:
        connection = connect_write_db() # <--- 写连接
        with connection.cursor() as cursor:
            ensure_table_exists(cursor)
            sql = "UPDATE iosCalendar SET status = %s, date_completed = %s, server_finishjob_date = NOW() WHERE id = %s"
            cursor.execute(sql, ('completed', date_completed, task_id))
        connection.commit()
        return jsonify({"message": "Task completed successfully"}), 200
    except Exception as e:
        print(f"Error completing task {task_id}:", e); traceback.print_exc()
        if connection: connection.rollback()
        return jsonify({"error": str(e)}), 500
    finally:
        if connection: connection.close()

# 获取待办任务 (需要添加 @connects_to_db, 适配 DictCursor)
@app.route('/getPendingTasks', methods=['GET'])
@connects_to_db # <-- 添加装饰器
def get_pending_tasks():
    try:
        limit = int(request.args.get('limit', default=10, type=int)) # 增加默认 limit
        offset = int(request.args.get('offset', default=0, type=int))
    except ValueError:
        return jsonify({"error": "Invalid pagination parameters"}), 400


    if limit < 1 or offset < 0:
        return jsonify({"error": "Invalid pagination parameters"}), 400

    connection = None
    try:
        connection = connect_read_db() # <--- 读连接
        with connection.cursor() as cursor:
            # ensure_table_exists(cursor) # 只读模式通常不创建表
            sql_count = "SELECT COUNT(*) as count FROM iosCalendar WHERE status = %s"
            cursor.execute(sql_count, ('pending',))
            total_count = cursor.fetchone()['count'] # 使用字典键

            tasks = []
            if total_count > 0 and offset < total_count:
                 sql = """
                     SELECT id, task, status, date_added
                     FROM iosCalendar
                     WHERE status = %s
                     ORDER BY id ASC
                     LIMIT %s OFFSET %s
                 """
                 cursor.execute(sql, ('pending', limit, offset))
                 tasks = cursor.fetchall() # 返回字典列表

            # result 不需要转换
            return jsonify({"tasks": tasks, "total_count": total_count}), 200
    except Exception as e:
        print("Error fetching pending tasks:", e); traceback.print_exc()
        return jsonify({"error": str(e)}), 500
    finally:
        if connection: connection.close()

# 获取总任务数 (适配 DictCursor)
@app.route('/getTotalTaskCount', methods=['GET'])
@connects_to_db
def get_total_task_count():
    connection = None
    try:
        connection = connect_read_db() # <--- 读连接
        with connection.cursor() as cursor:
            # ensure_table_exists(cursor)
            sql = "SELECT COUNT(*) as count FROM iosCalendar WHERE status = %s"
            cursor.execute(sql, ('pending',))
            total_count = cursor.fetchone()['count'] # 使用字典键
            return jsonify({"totalCount": total_count}), 200
    except Exception as e:
        print("Error getting total task count:", e); traceback.print_exc()
        return jsonify({"error": str(e)}), 500
    finally:
        if connection: connection.close()

# 搜索任务 (需要添加 @connects_to_db, 适配 DictCursor)
@app.route('/searchTasks', methods=['GET'])
@connects_to_db # <-- 添加装饰器
def search_tasks():
    keyword = request.args.get('keyword', default='', type=str)
    if not keyword:
        return jsonify({"error": "Keyword cannot be empty"}), 400

    connection = None
    try:
        connection = connect_read_db() # <--- 读连接
        with connection.cursor() as cursor:
            # ensure_table_exists(cursor)
            sql = """
                SELECT id, task, status, date_added, date_completed
                FROM iosCalendar
                WHERE task LIKE %s
                ORDER BY date_added DESC
            """
            cursor.execute(sql, ('%' + keyword + '%',))
            tasks = cursor.fetchall() # 返回字典列表
            # result 不需要转换
            return jsonify(tasks), 200
    except Exception as e:
        print("Error searching tasks:", e); traceback.print_exc()
        return jsonify({"error": str(e)}), 500
    finally:
        if connection: connection.close()

# 按 ID 获取任务 (适配 DictCursor)
@app.route('/getTaskById', methods=['GET'])
@connects_to_db
def get_task_by_id():
    task_id = request.args.get('task_id', type=int)
    if not task_id: return jsonify({"error": "Missing task_id"}), 400

    connection = None
    try:
        connection = connect_read_db() # <--- 读连接
        with connection.cursor() as cursor:
            sql = "SELECT id, task, status, date_added, date_completed FROM iosCalendar WHERE id = %s"
            cursor.execute(sql, (task_id,))
            task = cursor.fetchone() # 返回字典或 None
            if task:
                return jsonify(task), 200
            else:
                return jsonify({"error": "Task not found"}), 404
    except Exception as e:
        print(f"Error getting task by ID {task_id}:", e); traceback.print_exc()
        return jsonify({"error": str(e)}), 500
    finally:
        if connection: connection.close()

# --- 记录 iOS 行为 ---
@app.route('/logAppBehavior', methods=['POST'])
@connects_to_db
def log_app_behavior():
    data = request.json
    # 提取所有可能的字段
    log_data = {
        'start_time': data.get('start_time'),
        'network_type_before': data.get('network_type_before'),
        'network_type_after': data.get('network_type_after'),
        'network_change_time': data.get('network_change_time'),
        'network_change_frequency': data.get('network_change_frequency'),
        'last_page_visited': data.get('last_page_visited'),
        'current_page_visited': data.get('current_page_visited'),
        'user_action': data.get('user_action'),
        'action_time': data.get('action_time'),
        'app_state': data.get('app_state'),
        'error_message': data.get('error_message'),
        'session_duration': data.get('session_duration'),
        'task_id': data.get('task_id'),
        'gps_location': data.get('gps_location'),
        'battery_level': data.get('battery_level'),
        'memory_usage': data.get('memory_usage'),
        'device_time': data.get('device_time')
    }

    # 简单验证必要字段
    if not log_data['start_time'] or not log_data['device_time']:
        return jsonify({"error": "Missing essential data (start_time or device_time)"}), 400


    connection = None
    try:
        connection = connect_write_db() # <--- 写连接
        with connection.cursor() as cursor:
            ensure_ios_app_behavior_table_exists(cursor) # 确保表存在

            # 动态构建插入语句
            columns = ', '.join(f"`{key}`" for key in log_data.keys())
            placeholders = ', '.join(['%s'] * len(log_data))
            sql = f"INSERT INTO ios_app_behavior_monitoring ({columns}) VALUES ({placeholders})"

            cursor.execute(sql, tuple(log_data.values()))
        connection.commit()
        return jsonify({"message": "App behavior logged successfully"}), 200
    except Exception as e:
        print("Error logging app behavior:", e); traceback.print_exc()
        if connection: connection.rollback()
        return jsonify({"error": str(e)}), 500
    finally:
        if connection: connection.close()

# --- 打印记录相关路由 ---
@app.route('/insertPrinterRecord', methods=['POST'])
@connects_to_db
def insert_printer_record():
    data = request.json
    task_id = data.get('task_id')
    task_name = data.get('task_name')
    gpt_response = data.get('gpt_response')
    usage_type = data.get('usage_type')
    save_date = data.get('save_date')
    file_data = data.get('file_data') # 前端应发送 Base64 编码的字符串
    file_path = data.get('file_path')
    gpt_response_type = data.get('gpt_response_type')

    # 验证必要字段
    if not all([task_id, task_name, usage_type, save_date, gpt_response_type]):
         return jsonify({"error": "Missing required fields for printer record"}), 400

    # 处理可能的 Base64 文件数据
    file_data_blob = None
    if file_data:
        try:
            file_data_blob = base64.b64decode(file_data)
        except (TypeError, ValueError):
             return jsonify({"error": "Invalid file_data format (must be Base64)"}), 400


    connection = None
    try:
        connection = connect_write_db() # <--- 写连接
        with connection.cursor() as cursor:
            ensure_printer_record_table_exists(cursor)
            sql = """
                INSERT INTO printer_record (task_id, task_name, gpt_response, usage_type, save_date, file_data, file_path, gpt_response_type)
                VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
            """
            cursor.execute(sql, (task_id, task_name, gpt_response, usage_type, save_date, file_data_blob, file_path, gpt_response_type))
        connection.commit()
        return jsonify({"message": "Record inserted successfully"}), 200
    except Exception as e:
        print("Error inserting printer record:", e); traceback.print_exc()
        if connection: connection.rollback()
        return jsonify({"error": str(e)}), 500
    finally:
        if connection: connection.close()

# 获取打印记录 (适配 DictCursor)
@app.route('/getPrinterRecords', methods=['GET'])
@connects_to_db
def get_printer_records():
    task_id = request.args.get('task_id')
    connection = None
    try:
        connection = connect_read_db() # <--- 读连接
        with connection.cursor() as cursor:
            ensure_printer_record_table_exists(cursor)
            # 查询时不选择 file_data (BLOB) 以提高性能
            sql = "SELECT id, task_id, task_name, gpt_response, usage_type, save_date, file_path, gpt_response_type FROM printer_record"
            params = []
            if task_id:
                sql += " WHERE task_id = %s"
                params.append(task_id)

            cursor.execute(sql, params)
            records = cursor.fetchall() # 字典列表
            # result 不需要转换
            return jsonify(records), 200
    except Exception as e:
        print("Error getting printer records:", e); traceback.print_exc()
        return jsonify({"error": str(e)}), 500
    finally:
        if connection: connection.close()

# 删除打印记录 (适配路由参数)
@app.route('/deletePrinterRecord/<int:record_id>', methods=['DELETE']) # <-- 路径参数
@connects_to_db
def delete_printer_record(record_id): # <-- 接收参数
    connection = None
    try:
        connection = connect_write_db() # <--- 写连接
        with connection.cursor() as cursor:
            # ensure_printer_record_table_exists(cursor) # 删除前通常不创建
            sql = "DELETE FROM printer_record WHERE id = %s"
            cursor.execute(sql, (record_id,)) # 使用路径参数
            rowcount = cursor.rowcount
        connection.commit()

        if rowcount == 0:
            return jsonify({"error": "Record not found"}), 404
        return jsonify({"message": "Record deleted successfully"}), 200
    except Exception as e:
        print(f"Error deleting printer record {record_id}:", e); traceback.print_exc()
        if connection: connection.rollback()
        return jsonify({"error": str(e)}), 500
    finally:
        if connection: connection.close()

# 更新打印记录 (适配路由参数)
@app.route('/updatePrinterRecord/<int:record_id>', methods=['PUT']) # <-- 路径参数
@connects_to_db
def update_printer_record(record_id): # <-- 接收参数
    data = request.json
    # 提取所有字段
    update_data = {
        'task_id': data.get('task_id'),
        'task_name': data.get('task_name'),
        'gpt_response': data.get('gpt_response'),
        'usage_type': data.get('usage_type'),
        'save_date': data.get('save_date'),
        'file_data': data.get('file_data'), # 同样需要 Base64 处理
        'file_path': data.get('file_path'),
        'gpt_response_type': data.get('gpt_response_type')
    }

    # 处理 Base64 文件数据
    file_data_blob = None
    if update_data['file_data']:
        try:
            file_data_blob = base64.b64decode(update_data['file_data'])
        except (TypeError, ValueError):
            return jsonify({"error": "Invalid file_data format (must be Base64)"}), 400
    update_data['file_data'] = file_data_blob


    # 动态构建 SET 子句，排除值为 None 的字段
    set_clauses = []
    values = []
    for key, value in update_data.items():
         if value is not None: # 只更新非 None 值
             set_clauses.append(f"`{key}` = %s")
             values.append(value)


    if not set_clauses:
        return jsonify({"message": "No fields to update"}), 200 # 或者返回 400？


    values.append(record_id) # 添加 WHERE 条件的值
    sql = f"UPDATE printer_record SET {', '.join(set_clauses)} WHERE id = %s"


    connection = None
    try:
        connection = connect_write_db() # <--- 写连接
        with connection.cursor() as cursor:
            # ensure_printer_record_table_exists(cursor) # 更新前通常不创建
            cursor.execute(sql, tuple(values))
            rowcount = cursor.rowcount
        connection.commit()

        if rowcount == 0:
             # 可能记录不存在，或者没有字段实际被更新（所有发送的值都与原值相同）
             # 检查记录是否存在以区分
             with connection.cursor() as cursor:
                  cursor.execute("SELECT id FROM printer_record WHERE id = %s", (record_id,))
                  if not cursor.fetchone():
                       return jsonify({"error": "Record not found"}), 404
        return jsonify({"message": "Record updated successfully"}), 200
    except Exception as e:
        print(f"Error updating printer record {record_id}:", e); traceback.print_exc()
        if connection: connection.rollback()
        return jsonify({"error": str(e)}), 500
    finally:
        if connection: connection.close()

# Main execution block
if __name__ == '__main__':
    # 启动后台线程
    threading.Thread(target=system_stats_loop, daemon=True).start()
    # 恢复为仅启动 HTTP 服务
    print("[INFO 5100] Starting Flask server on 0.0.0.0:5100 (HTTP)")
    # 使用 use_reloader=False 避免调试模式下线程重复启动
    app.run(host='0.0.0.0', port=5100, debug=True, use_reloader=False, threaded=True)